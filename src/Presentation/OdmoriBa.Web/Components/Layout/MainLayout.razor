@using Microsoft.AspNetCore.Authentication.OpenIdConnect
@inherits LayoutComponentBase

<MudThemeProvider Theme="@_theme" IsDarkMode="_isDarkMode"/>
<MudPopoverProvider/>
<MudDialogProvider/>
<MudSnackbarProvider/>

<MudLayout>
    <MudAppBar Dense="true" Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start"
                       OnClick="@(_ => _drawerOpen = !_drawerOpen)" Class="mr-2 d-flex d-md-none"/>
        <MudLink Href="/">
            <MudImage Src="/img/logo.png" Width="60"/>
        </MudLink>
        <MudSpacer/>

        <!-- Desktop navigation - visible on md screens and up -->
        <div class="d-none d-md-flex">
            <MudNavLink Href="/" IconColor="Color.Inherit" ActiveClass="border-t-4"
                        Icon="@Icons.Material.Filled.Home" Match="NavLinkMatch.All">Home
            </MudNavLink>
            <MudNavLink Href="/trips" IconColor="Color.Inherit" ActiveClass="border-t-4"
                        Icon="@Icons.Material.Filled.CardTravel" Match="NavLinkMatch.Prefix">Putovanja
            </MudNavLink>
            <MudNavLink Href="/destinations" IconColor="Color.Inherit" ActiveClass="border-t-4"
                        Icon="@Icons.Material.Filled.ModeOfTravel" Match="NavLinkMatch.Prefix">Destinacije
            </MudNavLink>
            <MudNavLink Href="/persons" IconColor="Color.Inherit" ActiveClass="border-t-4"
                        Icon="@Icons.Material.Filled.People" Match="NavLinkMatch.Prefix">Osobe
            </MudNavLink>
            <MudNavLink Href="/settings" IconColor="Color.Inherit" ActiveClass="border-t-4"
                        Icon="@Icons.Material.Filled.Settings" Match="NavLinkMatch.Prefix">Postavke
            </MudNavLink>
        </div>

        <MudSpacer/>
        <MudIconButton Icon="@(DarkLightModeButtonIcon)" Color="Color.Inherit" OnClick="@DarkModeToggle"/>

        <AuthorizeView>
            <Authorized Context="context">
                <MudButton StartIcon="@Icons.Material.Filled.AccountCircle"
                           Color="Color.Inherit"
                           OnClick="@Logout">
                    @context.User.Identity?.Name!
                </MudButton>
            </Authorized>
        </AuthorizeView>
    </MudAppBar>

    <!-- Mobile navigation drawer - only shows on small screens -->
    <MudDrawer @bind-Open="_drawerOpen" Elevation="1" Variant="@DrawerVariant.Temporary"
               ClipMode="DrawerClipMode.Always">
        <MudDrawerHeader>
            <MudText Typo="Typo.h6">odmori.ba | Admin</MudText>
        </MudDrawerHeader>
        <MudNavMenu>
            <MudNavLink Href="/" Icon="@Icons.Material.Filled.Home" Match="NavLinkMatch.All">
                Home
            </MudNavLink>
            <MudNavLink Href="/trips" Icon="@Icons.Material.Filled.CardTravel" Match="NavLinkMatch.Prefix">
                Putovanja
            </MudNavLink>
            <MudNavLink Href="/destinations" Icon="@Icons.Material.Filled.ModeOfTravel" Match="NavLinkMatch.Prefix">
                Destinacije
            </MudNavLink>
            <MudNavLink Href="/persons" Icon="@Icons.Material.Filled.People" Match="NavLinkMatch.Prefix">
                Osobe
            </MudNavLink>
            <MudNavLink Href="/settings" Icon="@Icons.Material.Filled.Settings" Match="NavLinkMatch.Prefix">
                Postavke
            </MudNavLink>
        </MudNavMenu>
    </MudDrawer>

    <MudMainContent Class="mt-10 mb-3 pa-4">
        @Body
    </MudMainContent>

    <MudAppBar Style="height: 24px" Bottom="true" Dense="true" Elevation="1">
        <MudSpacer/>
        <MudText Typo="Typo.body2" Class="mx-auto">©@DateTime.Now.Year Odmori.ba Travels®</MudText>
        <MudSpacer/>
        <MudText Typo="Typo.body2" Class="mx-auto">v1.0</MudText>
    </MudAppBar>
</MudLayout>

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    private bool _isDarkMode;
    private bool _drawerOpen;
    private readonly MudTheme? _theme = Theme.MainTheme;

    [Inject] NavigationManager NavigationManager { get; set; } = null!;

    // Toggles Dark Mode
    private void DarkModeToggle() => _isDarkMode = !_isDarkMode;

    private void Logout()
    {
        NavigationManager.NavigateTo($"MicrosoftIdentity/Account/SignOut/{OpenIdConnectDefaults.AuthenticationScheme}", forceLoad: true);
    }

    private string DarkLightModeButtonIcon => _isDarkMode
        ? Icons.Material.Rounded.AutoMode // Light mode icon
        : Icons.Material.Outlined.DarkMode; // Dark mode icon

}