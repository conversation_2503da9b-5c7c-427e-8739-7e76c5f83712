using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting.StaticWebAssets;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.UI;
using MudBlazor.Services;
using MudExtensions.Services;
using OdmoriBa.Application;
using OdmoriBa.Infrastructure;
using OdmoriBa.JobProcessing;
using OdmoriBa.Presentation;
using OdmoriBa.Web;
using OdmoriBa.Web.Components;

var builder = WebApplication.CreateBuilder(args);

if (builder.Environment.IsLocal())
{
    builder.Configuration.AddUserSecrets<Program>();
}

StaticWebAssetsLoader.UseStaticWebAssets(builder.Environment, builder.Configuration);

// 1. Configure Azure AD Authentication
builder.Services.AddAuthentication(OpenIdConnectDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApp(options =>
    {
        builder.Configuration.GetSection("AzureAd").Bind(options);

        options.Events = new OpenIdConnectEvents
        {
            OnTokenValidated = async context =>
            {
                var eventHandler = context.HttpContext.RequestServices
                    .GetRequiredService<OdmoriBa.Web.Services.Auth.AuthenticationEventHandler>();
                await eventHandler.OnTokenValidated(context);
            }
        };
    });

builder.Services.AddControllersWithViews().AddMicrosoftIdentityUI();

builder.Services.AddAuthorizationBuilder()
    .SetFallbackPolicy(new AuthorizationPolicyBuilder()
        .RequireAuthenticatedUser()
        .Build());

builder.Services.AddCascadingAuthenticationState();

// Add MudBlazor services
builder.Services.AddMudServices();

builder.Services.AddMudExtensions();

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

builder.Services.AddJobProcessing();

builder.AddPresentation();
builder.Services.AddInfrastructureWeb(builder.Configuration);
builder.Services.AddApplication(builder.Configuration);
builder.Services.AddWeb();

var app = builder.Build();

app.UsePresentation();

// Configure the HTTP request pipeline.
if (app.Environment.IsProduction())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseStatusCodePages(context =>
{
    var request = context.HttpContext.Request;
    var response = context.HttpContext.Response;

    // Don't redirect authentication-related paths to 404
    if (request.Path.StartsWithSegments("/MicrosoftIdentity") ||
        request.Path.StartsWithSegments("/signin-oidc") ||
        request.Path.StartsWithSegments("/signout-oidc") ||
        request.Path.StartsWithSegments("/signout"))
    {
        return Task.CompletedTask;
    }

    // Only redirect 404s to our custom 404 page
    if (response.StatusCode == 404)
    {
        response.Redirect("/404");
    }

    return Task.CompletedTask;
});

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

// app.MapControllers();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .RequireAuthorization();

app.Run();