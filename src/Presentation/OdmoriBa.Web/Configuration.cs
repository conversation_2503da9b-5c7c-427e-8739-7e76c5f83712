using OdmoriBa.Application.Interfaces.Auth;
using OdmoriBa.Web.Components.Pages.Destinations.Destination;
using OdmoriBa.Web.Components.Pages.Persons.Person;
using OdmoriBa.Web.Components.Pages.Trips.Trip;
using OdmoriBa.Web.Services.Auth;

namespace OdmoriBa.Web;

public static class Configuration
{
    public static IServiceCollection AddWeb(this IServiceCollection services)
    {
        services.AddHttpContextAccessor();
        services.AddScoped<IIdentityService, IdentityWebService>();
        services.AddScoped<AuthenticationEventHandler>();

        return services.AddState();
    }

    public static IServiceCollection AddState(this IServiceCollection services)
    {
        services.AddScoped<TripState>();
        services.AddScoped<DestinationState>();
        services.AddScoped<PersonState>();
        return services;
    }
}