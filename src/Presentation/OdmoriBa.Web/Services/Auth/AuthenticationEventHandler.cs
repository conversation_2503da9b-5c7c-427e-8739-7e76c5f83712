using System.Security.Claims;
using Mediator;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.EntityFrameworkCore;
using OdmoriBa.Application.Features.Customers.Commands;
using OdmoriBa.Application.Interfaces.Data;

namespace OdmoriBa.Web.Services.Auth;

public sealed class AuthenticationEventHandler(
    IServiceProvider serviceProvider,
    ILogger<AuthenticationEventHandler> logger)
{
    public async Task OnTokenValidated(TokenValidatedContext context)
    {
        try
        {
            using var scope = serviceProvider.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var dbContext = scope.ServiceProvider.GetRequiredService<IAppDbContext>();

            var identityId = context.Principal?.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(identityId))
            {
                logger.LogWarning("No NameIdentifier claim found in token");
                return;
            }

            // Check if user already exists in our database
            var existingUser = await dbContext.Users
                .FirstOrDefaultAsync(u => u.IdentityId == identityId, CancellationToken.None);

            if (existingUser != null)
            {
                // User exists, add sys_id claim
                var identity = (ClaimsIdentity)context.Principal!.Identity!;
                identity.AddClaim(new Claim("sys_id", existingUser.Id.ToString()));
                logger.LogDebug("Added sys_id claim for existing user {UserId}", existingUser.Id);
                return;
            }

            // User doesn't exist, create new admin user
            var firstName = context.Principal?.FindFirstValue(ClaimTypes.GivenName) ?? "Admin";
            var lastName = context.Principal?.FindFirstValue(ClaimTypes.Surname) ?? "User";
            var email = context.Principal?.FindFirstValue(ClaimTypes.Email) ?? 
                       context.Principal?.FindFirstValue("preferred_username") ?? 
                       "<EMAIL>";

            var command = new CreateAdminCommand(identityId, firstName, lastName, email);
            var result = await mediator.Send(command);

            if (result.IsError)
            {
                logger.LogError("Failed to create admin user for {IdentityId}: {Error}", identityId, result.Error);
                context.Fail($"Failed to create user: {result.Error}");
                return;
            }

            // Add sys_id claim for the newly created user
            var userIdentity = (ClaimsIdentity)context.Principal!.Identity!;
            userIdentity.AddClaim(new Claim("sys_id", result.Value.Id.ToString()));
            
            logger.LogInformation("Created new admin user {UserId} for {IdentityId}", result.Value.Id, identityId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during token validation");
            context.Fail("Authentication failed");
        }
    }
}
