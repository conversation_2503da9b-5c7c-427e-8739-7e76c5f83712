using System.Security.Claims;
using Mediator;
using Microsoft.IdentityModel.Tokens;
using OdmoriBa.Application.Features.Customers.Commands;
using OdmoriBa.Application.Interfaces.Auth;

namespace OdmoriBa.Web.Services.Auth;

public sealed class IdentityWebService(
    IHttpContextAccessor httpContextAccessor, 
    IMediator mediator,
    ILogger<IdentityWebService> logger) : IIdentityService
{
    private readonly ClaimsPrincipal _claimsPrincipal = httpContextAccessor.HttpContext?.User ??
                                                        throw new SecurityTokenException("Invalid token");

    public string IdentityId => _claimsPrincipal.FindFirstValue(ClaimTypes.NameIdentifier) ??
                                 throw new SecurityTokenException("Invalid token");

    private Guid? _userId;
    public Guid UserId
    {
        get
        {
            if (_userId.HasValue) return _userId.Value;
            
            // Try to get user ID from custom claim first (if user exists in our database)
            var userIdClaim = _claimsPrincipal.FindFirstValue("sys_id");
            if (userIdClaim != null && Guid.TryParse(userIdClaim, out var existingUserId))
            {
                _userId = existingUserId;
                return _userId.Value;
            }

            // If no custom claim exists, this is a first-time login - create admin user
            _userId = CreateAdminUserAsync().GetAwaiter().GetResult();
            return _userId.Value;
        }
    }

    private async Task<Guid> CreateAdminUserAsync()
    {
        try
        {
            var firstName = _claimsPrincipal.FindFirstValue(ClaimTypes.GivenName) ?? "Admin";
            var lastName = _claimsPrincipal.FindFirstValue(ClaimTypes.Surname) ?? "User";
            var email = _claimsPrincipal.FindFirstValue(ClaimTypes.Email) ?? 
                       _claimsPrincipal.FindFirstValue("preferred_username") ?? 
                       "<EMAIL>";

            var command = new CreateAdminCommand(IdentityId, firstName, lastName, email);
            var result = await mediator.Send(command);

            if (result.IsError)
            {
                logger.LogError("Failed to create admin user for {IdentityId}: {Error}", IdentityId, result.Error);
                throw new InvalidOperationException($"Failed to create admin user: {result.Error}");
            }

            logger.LogInformation("Created new admin user {UserId} for {IdentityId}", result.Value.Id, IdentityId);
            return result.Value.Id;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating admin user for {IdentityId}", IdentityId);
            throw;
        }
    }
}
