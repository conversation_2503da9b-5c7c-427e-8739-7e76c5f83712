using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using OdmoriBa.Application.Interfaces.Auth;

namespace OdmoriBa.Web.Services.Auth;

public sealed class IdentityWebService(IHttpContextAccessor httpContextAccessor) : IIdentityService
{
    private readonly ClaimsPrincipal _claimsPrincipal = httpContextAccessor.HttpContext?.User ??
                                                        throw new SecurityTokenException("Invalid token");

    public string IdentityId => _claimsPrincipal.FindFirstValue(ClaimTypes.NameIdentifier) ??
                                 throw new SecurityTokenException("Invalid token");

    public Guid UserId
    {
        get
        {
            // Get user ID from custom claim (should be set during login process)
            var userIdClaim = _claimsPrincipal.FindFirstValue("sys_id");
            if (userIdClaim != null && Guid.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }

            throw new SecurityTokenException("User ID not found in claims. User may not be properly registered.");
        }
    }
}
