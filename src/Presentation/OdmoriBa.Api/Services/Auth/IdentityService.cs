using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using OdmoriBa.Application.Common.Constants;
using OdmoriBa.Application.Interfaces.Auth;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Api.Services.Auth;

public sealed class IdentityService(IHttpContextAccessor httpContextAccessor) : IIdentityService
{
    private readonly ClaimsPrincipal _claimsPrincipal = httpContextAccessor.HttpContext?.User ??
                                                        throw new SecurityTokenException("Invalid token");

    public string IdentityId => _claimsPrincipal.FindFirstValue(CustomClaimTypes.IdentityId) ??
                                 throw new SecurityTokenException("Invalid token");

    public Guid UserId => Guid.Parse(_claimsPrincipal.FindFirstValue(CustomClaimTypes.SysId) ??
                                     throw new SecurityTokenException("Invalid token"));
}