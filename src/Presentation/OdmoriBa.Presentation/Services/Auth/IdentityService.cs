using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;
using OdmoriBa.Application.Common.Constants;
using OdmoriBa.Application.Interfaces.Auth;

namespace OdmoriBa.Presentation.Services.Auth;

public sealed class IdentityService(IHttpContextAccessor httpContextAccessor) : IIdentityService
{
    private readonly ClaimsPrincipal _claimsPrincipal = httpContextAccessor.HttpContext?.User ??
                                                        throw new SecurityTokenException("Invalid token");

    public string IdentityId =>
        _claimsPrincipal.FindFirstValue(CustomClaimTypes.IdentityId) ??
        _claimsPrincipal.FindFirstValue(ClaimTypes.NameIdentifier) ??
        throw new SecurityTokenException("Invalid token");

    public Guid UserId
    {
        get
        {
            var userIdClaim = _claimsPrincipal.FindFirstValue(CustomClaimTypes.SysId) ??
                             _claimsPrincipal.FindFirstValue("sys_id");

            if (userIdClaim != null && Guid.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }

            throw new SecurityTokenException("User ID not found in claims. User may not be properly registered.");
        }
    }
}