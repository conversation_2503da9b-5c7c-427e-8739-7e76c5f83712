using OdmoriBa.Application.Common.Attributes;
using OdmoriBa.Application.Features.Customers.Mappers;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Commands;

public sealed record CreateAdminCommand(
    string IdentityId,
    [property: MaskName] string FirstName,
    [property: MaskName] string LastName,
    [property: MaskEmail] string Email) : ICommand<Result<UserDto>>;

internal sealed class CreateAdminCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateAdminCommand, Result<UserDto>>
{
    public async ValueTask<Result<UserDto>> Handle(CreateAdminCommand request, CancellationToken cancellationToken)
    {
        // Check if user already exists
        var existingUser = await dbContext.Users
            .Include(u => u.Person)
            .FirstOrDefaultAsync(s => s.IdentityId == request.IdentityId, cancellationToken);

        if (existingUser is not null) 
            return existingUser.ToDto(true)!;

        // Create admin user with minimal required information
        var result = User.CreateWithPerson(
            request.IdentityId,
            UserRole.Admin,
            "AzureAD", // Sign-in provider for web admins
            request.FirstName,
            request.LastName,
            request.Email,
            new Phone("+397", "111111111"), // Empty phone for now, can be updated later
            DateOnly.FromDateTime(DateTime.Now.AddYears(-25)), // Default birth date, can be updated later
            null, // No ID document required for admin
            null, // No country code
            null, // No city
            null  // No address
        );

        if (result.IsError) return result.Error;

        var user = result.Value;

        await using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

        dbContext.Users.Add(user);
        await dbContext.SaveChangesAsync(cancellationToken);

        await transaction.CommitAsync(cancellationToken);

        return user.ToDto(true)!;
    }
}
