using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using OdmoriBa.Application.Interfaces.Auth;
using OdmoriBa.Core.Common;

namespace OdmoriBa.Infrastructure.Data.Interceptors;

public sealed class AuditableEntityInterceptor(IIdentityService? identityService = null) : SaveChangesInterceptor
{
    public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
    {
        UpdateEntities(eventData.Context);

        return base.SavingChanges(eventData, result);
    }

    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(DbContextEventData eventData, InterceptionResult<int> result, CancellationToken cancellationToken = default)
    {
        UpdateEntities(eventData.Context);

        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private void UpdateEntities(DbContext? context)
    {
        if (context == null) return;

        Guid? currentUserId = null;
        try
        {
            currentUserId = identityService?.UserId;
        }
        catch
        {
            // Ignore errors when getting user ID (e.g., during migrations, background jobs, etc.)
        }

        foreach (var entry in context.ChangeTracker.Entries<EntityBase>())
        {
            if (entry.State is EntityState.Added or EntityState.Modified || entry.HasChangedOwnedEntities())
            {
                var utcNow = DateTimeOffset.UtcNow;
                if (entry.State == EntityState.Added)
                {
                    entry.Property(nameof(EntityBase.CreatedAt)).CurrentValue = utcNow;
                    if (currentUserId.HasValue)
                    {
                        entry.Property(nameof(EntityBase.CreatedBy)).CurrentValue = currentUserId.Value;
                    }
                }
                entry.Property(nameof(EntityBase.UpdatedAt)).CurrentValue = utcNow;
                if (currentUserId.HasValue)
                {
                    entry.Property(nameof(EntityBase.UpdatedBy)).CurrentValue = currentUserId.Value;
                }
            }
        }
    }
}

public static class Extensions
{
    public static bool HasChangedOwnedEntities(this EntityEntry entry) =>
        entry.References.Any(r => 
            r.TargetEntry != null && 
            r.TargetEntry.Metadata.IsOwned() && 
            r.TargetEntry.State is EntityState.Added or EntityState.Modified);
}
